#!/usr/bin/env python3
"""
Test script for verifying the deployment of YouTube Transcript Tutor.
Run this script to check if all components are working correctly.
"""

import os
import sys
import importlib.util

def test_imports():
    """Test if all required modules can be imported."""
    print("🔍 Testing imports...")
    
    required_modules = [
        'streamlit',
        'pytube',
        'youtube_transcript_api',
        'langchain',
        'openai',
        'faiss',
        'tiktoken',
        'reportlab',
        'requests',
        'yaml',
        'dotenv'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            if module == 'faiss':
                import faiss
            elif module == 'yaml':
                import yaml
            elif module == 'dotenv':
                from dotenv import load_dotenv
            else:
                __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ All imports successful!")
        return True

def test_file_structure():
    """Test if all required files are present."""
    print("\n🔍 Testing file structure...")
    
    required_files = [
        'app.py',
        'requirements.txt',
        'README.md',
        'static/style.css',
        '.streamlit/config.toml',
        'src/__init__.py',
        'src/utils/__init__.py',
        'src/utils/youtube_handler.py',
        'src/utils/text_processor.py',
        'src/utils/session_manager.py',
        'src/utils/export_utils.py',
        'src/utils/logger.py',
        'config/__init__.py',
        'config/settings.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ All required files present!")
        return True

def test_app_import():
    """Test if the main app can be imported."""
    print("\n🔍 Testing app import...")
    
    try:
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Try to import main components
        from src.utils.youtube_handler import YouTubeHandler
        from src.utils.text_processor import TextProcessor
        from src.utils.session_manager import SessionManager
        from src.utils.export_utils import ExportUtils
        from src.utils.logger import setup_logging
        from config.settings import settings
        
        print("  ✅ All app components imported successfully!")
        return True
        
    except Exception as e:
        print(f"  ❌ Failed to import app components: {e}")
        return False

def test_environment():
    """Test environment configuration."""
    print("\n🔍 Testing environment...")
    
    # Check for OpenAI API key
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        print("  ✅ OPENAI_API_KEY found in environment")
    else:
        print("  ⚠️  OPENAI_API_KEY not found in environment (will need to be set in Hugging Face Spaces)")
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"  ✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"  ❌ Python version too old: {python_version.major}.{python_version.minor}.{python_version.micro}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🚀 Testing YouTube Transcript Tutor Deployment\n")
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("App Components", test_app_import),
        ("Environment", test_environment)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Ready for deployment to Hugging Face Spaces!")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues before deploying.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
