2025-07-13 22:55:30,859 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:55:37,615 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:56:36,886 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:56:40,977 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:56:54,360 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:57:04,282 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:58:12,592 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:58:21,552 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:58:38,183 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:58:41,834 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:59:16,207 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:59:22,975 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:59:23,716 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:59:26,323 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 23:01:13,950 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:01:35,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:01:35,774 - openai._base_client - INFO - Retrying request to /embeddings in 0.378161 seconds
2025-07-13 23:01:37,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:01:37,505 - openai._base_client - INFO - Retrying request to /embeddings in 0.796060 seconds
2025-07-13 23:01:39,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:01:39,286 - src.utils.text_processor - ERROR - Error creating vector store: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:02:22,588 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:02:36,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:02:36,285 - openai._base_client - INFO - Retrying request to /embeddings in 0.379324 seconds
2025-07-13 23:02:37,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:02:37,476 - openai._base_client - INFO - Retrying request to /embeddings in 0.943958 seconds
2025-07-13 23:02:39,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:02:39,328 - src.utils.text_processor - ERROR - Error creating vector store: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:09:22,969 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:09:26,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:09:26,986 - openai._base_client - INFO - Retrying request to /embeddings in 0.395765 seconds
2025-07-13 23:09:27,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:09:27,913 - openai._base_client - INFO - Retrying request to /embeddings in 0.940555 seconds
2025-07-13 23:09:29,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:09:29,554 - src.utils.text_processor - WARNING - OpenAI embeddings failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:09:29,554 - src.utils.text_processor - INFO - Using simple text-based fallback
2025-07-13 23:09:29,554 - src.utils.text_processor - INFO - Created simple text-based fallback vector store
2025-07-13 23:09:29,555 - src.utils.text_processor - INFO - Using simple fallback QA system
2025-07-13 23:15:01,397 - src.utils.logger - INFO - Custom CSS loaded successfully
2025-07-13 23:15:05,056 - src.utils.logger - INFO - Custom CSS loaded successfully
2025-07-13 23:15:15,923 - src.utils.logger - INFO - Custom CSS loaded successfully
2025-07-13 23:15:17,491 - src.utils.logger - INFO - Custom CSS loaded successfully
2025-07-13 23:15:19,654 - src.utils.logger - INFO - Custom CSS loaded successfully
2025-07-13 23:15:23,535 - src.utils.logger - INFO - Custom CSS loaded successfully
2025-07-13 23:16:06,979 - src.utils.logger - INFO - Custom CSS loaded successfully
2025-07-13 23:21:32,219 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:21:43,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:21:43,013 - openai._base_client - INFO - Retrying request to /embeddings in 0.396331 seconds
2025-07-13 23:21:44,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:21:44,680 - openai._base_client - INFO - Retrying request to /embeddings in 0.842338 seconds
2025-07-13 23:21:47,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:21:47,128 - src.utils.text_processor - WARNING - OpenAI embeddings failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:21:47,129 - src.utils.text_processor - INFO - Using simple text-based fallback
2025-07-13 23:21:47,129 - src.utils.text_processor - INFO - Created simple text-based fallback vector store
2025-07-13 23:21:47,129 - src.utils.text_processor - INFO - Using simple fallback QA system
2025-07-13 23:22:46,498 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-13 23:22:49,535 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-13 23:53:47,078 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-13 23:53:52,909 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-13 23:53:59,444 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-13 23:53:59,609 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-13 23:54:00,519 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:54:07,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:54:07,688 - openai._base_client - INFO - Retrying request to /embeddings in 0.454709 seconds
2025-07-13 23:54:08,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:54:08,674 - openai._base_client - INFO - Retrying request to /embeddings in 0.918276 seconds
2025-07-13 23:54:10,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:54:10,656 - src.utils.text_processor - WARNING - OpenAI embeddings failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:54:10,657 - src.utils.text_processor - INFO - Using simple text-based fallback
2025-07-13 23:54:10,659 - src.utils.text_processor - INFO - Created simple text-based fallback vector store
2025-07-13 23:54:10,660 - src.utils.text_processor - INFO - Using simple fallback QA system
2025-07-13 23:54:22,185 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-13 23:54:24,094 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 00:42:29,448 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 00:42:47,515 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 00:42:47,673 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 00:42:48,260 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-14 00:42:52,222 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-14 00:42:52,224 - openai._base_client - INFO - Retrying request to /embeddings in 0.396998 seconds
2025-07-14 00:42:53,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-14 00:42:53,419 - openai._base_client - INFO - Retrying request to /embeddings in 0.829603 seconds
2025-07-14 00:42:54,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-14 00:42:54,713 - src.utils.text_processor - WARNING - OpenAI embeddings failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-14 00:42:54,715 - src.utils.text_processor - INFO - Using simple text-based fallback
2025-07-14 00:42:54,717 - src.utils.text_processor - INFO - Created simple text-based fallback vector store
2025-07-14 00:42:54,718 - src.utils.text_processor - INFO - Using simple fallback QA system
2025-07-14 00:50:26,573 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:00:15,758 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:00:23,869 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:00:24,021 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:00:24,480 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-14 01:00:26,491 - src.utils.youtube_handler - INFO - Successfully got transcript in en
2025-07-14 01:00:28,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-14 01:00:28,436 - openai._base_client - INFO - Retrying request to /embeddings in 0.464677 seconds
2025-07-14 01:00:29,888 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-14 01:00:29,889 - openai._base_client - INFO - Retrying request to /embeddings in 0.932156 seconds
2025-07-14 01:00:31,765 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-14 01:00:31,768 - src.utils.text_processor - WARNING - OpenAI embeddings failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-14 01:00:31,770 - src.utils.text_processor - INFO - Using simple text-based fallback
2025-07-14 01:00:31,771 - src.utils.text_processor - INFO - Created simple text-based fallback vector store
2025-07-14 01:00:31,772 - src.utils.text_processor - INFO - Using simple fallback QA system
2025-07-14 01:01:09,650 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:32,106 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:32,361 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:33,814 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:33,972 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:34,156 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:34,479 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:34,750 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:34,892 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:35,077 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:35,399 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:38,181 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:38,429 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:39,552 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:39,800 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:39,822 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:39,989 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:40,370 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:01:41,960 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:18,892 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:53,458 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:53,729 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:54,391 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:54,504 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:54,695 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:54,820 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:55,091 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:55,861 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:55,967 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:56,154 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:56,488 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:56,756 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:56,902 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:57,250 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:58,213 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:58,299 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:58,452 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:58,584 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:58,780 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:59,089 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:14:59,926 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:15:00,032 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:15:00,208 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
2025-07-14 01:15:00,533 - src.utils.logger - INFO - Custom dark theme CSS loaded successfully
