2025-07-13 22:55:30,859 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:55:37,615 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:56:36,886 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:56:40,977 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:56:54,360 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:57:04,282 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:58:12,592 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:58:21,552 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:58:38,183 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:58:41,834 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:59:16,207 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:59:22,975 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 22:59:23,716 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 22:59:26,323 - src.utils.youtube_handler - ERROR - Unexpected error getting transcript: 'FetchedTranscriptSnippet' object is not subscriptable
2025-07-13 23:01:13,950 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:01:35,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:01:35,774 - openai._base_client - INFO - Retrying request to /embeddings in 0.378161 seconds
2025-07-13 23:01:37,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:01:37,505 - openai._base_client - INFO - Retrying request to /embeddings in 0.796060 seconds
2025-07-13 23:01:39,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:01:39,286 - src.utils.text_processor - ERROR - Error creating vector store: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:02:22,588 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:02:36,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:02:36,285 - openai._base_client - INFO - Retrying request to /embeddings in 0.379324 seconds
2025-07-13 23:02:37,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:02:37,476 - openai._base_client - INFO - Retrying request to /embeddings in 0.943958 seconds
2025-07-13 23:02:39,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:02:39,328 - src.utils.text_processor - ERROR - Error creating vector store: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:09:22,969 - src.utils.youtube_handler - ERROR - Error getting video metadata: HTTP Error 400: Bad Request
2025-07-13 23:09:26,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:09:26,986 - openai._base_client - INFO - Retrying request to /embeddings in 0.395765 seconds
2025-07-13 23:09:27,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:09:27,913 - openai._base_client - INFO - Retrying request to /embeddings in 0.940555 seconds
2025-07-13 23:09:29,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-07-13 23:09:29,554 - src.utils.text_processor - WARNING - OpenAI embeddings failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-13 23:09:29,554 - src.utils.text_processor - INFO - Using simple text-based fallback
2025-07-13 23:09:29,554 - src.utils.text_processor - INFO - Created simple text-based fallback vector store
2025-07-13 23:09:29,555 - src.utils.text_processor - INFO - Using simple fallback QA system
