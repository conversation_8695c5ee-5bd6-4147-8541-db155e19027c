"""
Text processing utilities for document handling and vector store operations.
"""

import os
import logging
from typing import List, Optional, Dict, Any
from langchain_community.embeddings import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import CharacterTextSplitter, RecursiveCharacterTextSplitter
from langchain.chains import RetrievalQA
from langchain_community.llms import OpenAI
from langchain.docstore.document import Document
import pickle

logger = logging.getLogger(__name__)

class TextProcessor:
    """Handles text processing, document splitting, and vector store operations."""
    
    def __init__(self, openai_api_key: str):
        """
        Initialize TextProcessor with OpenAI API key.
        
        Args:
            openai_api_key (str): OpenAI API key
        """
        self.openai_api_key = openai_api_key
        self.embeddings = OpenAIEmbeddings(openai_api_key=openai_api_key)
        self.llm = OpenAI(openai_api_key=openai_api_key, temperature=0.7)
        
    def create_documents_from_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """
        Create LangChain documents from text with metadata.
        
        Args:
            text (str): Input text
            metadata (Dict[str, Any]): Document metadata
            
        Returns:
            List[Document]: List of LangChain documents
        """
        if metadata is None:
            metadata = {}
            
        # Use RecursiveCharacterTextSplitter for better text splitting
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
        
        # Create a document and split it
        doc = Document(page_content=text, metadata=metadata)
        docs = text_splitter.split_documents([doc])
        
        return docs
    
    def create_vector_store(self, documents: List[Document]) -> Optional[FAISS]:
        """
        Create FAISS vector store from documents.
        
        Args:
            documents (List[Document]): List of documents
            
        Returns:
            Optional[FAISS]: FAISS vector store or None if failed
        """
        try:
            if not documents:
                logger.error("No documents provided for vector store creation")
                return None
                
            vectorstore = FAISS.from_documents(documents, self.embeddings)
            return vectorstore
        except Exception as e:
            logger.error(f"Error creating vector store: {e}")
            return None
    
    def save_vector_store(self, vectorstore: FAISS, path: str) -> bool:
        """
        Save vector store to disk.
        
        Args:
            vectorstore (FAISS): Vector store to save
            path (str): Path to save the vector store
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            os.makedirs(os.path.dirname(path) if os.path.dirname(path) else '.', exist_ok=True)
            vectorstore.save_local(path)
            return True
        except Exception as e:
            logger.error(f"Error saving vector store: {e}")
            return False
    
    def load_vector_store(self, path: str) -> Optional[FAISS]:
        """
        Load vector store from disk.
        
        Args:
            path (str): Path to load the vector store from
            
        Returns:
            Optional[FAISS]: Loaded vector store or None if failed
        """
        try:
            if not os.path.exists(path):
                logger.error(f"Vector store path does not exist: {path}")
                return None
                
            vectorstore = FAISS.load_local(path, self.embeddings)
            return vectorstore
        except Exception as e:
            logger.error(f"Error loading vector store: {e}")
            return None
    
    def create_qa_chain(self, vectorstore: FAISS, chain_type: str = "stuff") -> Optional[RetrievalQA]:
        """
        Create QA chain from vector store.
        
        Args:
            vectorstore (FAISS): Vector store
            chain_type (str): Type of chain to create
            
        Returns:
            Optional[RetrievalQA]: QA chain or None if failed
        """
        try:
            retriever = vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": 4}
            )
            
            qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type=chain_type,
                retriever=retriever,
                return_source_documents=True
            )
            
            return qa_chain
        except Exception as e:
            logger.error(f"Error creating QA chain: {e}")
            return None
    
    def process_transcript(self, transcript_text: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process transcript text and create QA chain.
        
        Args:
            transcript_text (str): Transcript text
            metadata (Dict[str, Any]): Video metadata
            
        Returns:
            Dict[str, Any]: Processing result with QA chain and vector store
        """
        result = {
            'success': False,
            'qa_chain': None,
            'vectorstore': None,
            'documents': None,
            'error': None
        }
        
        try:
            # Create documents from transcript
            documents = self.create_documents_from_text(transcript_text, metadata)
            if not documents:
                result['error'] = "Failed to create documents from transcript"
                return result
            
            # Create vector store
            vectorstore = self.create_vector_store(documents)
            if not vectorstore:
                result['error'] = "Failed to create vector store"
                return result
            
            # Create QA chain
            qa_chain = self.create_qa_chain(vectorstore)
            if not qa_chain:
                result['error'] = "Failed to create QA chain"
                return result
            
            result['success'] = True
            result['qa_chain'] = qa_chain
            result['vectorstore'] = vectorstore
            result['documents'] = documents
            
        except Exception as e:
            result['error'] = f"Error processing transcript: {str(e)}"
            logger.error(f"Error processing transcript: {e}")
        
        return result
    
    def ask_question(self, qa_chain: RetrievalQA, question: str) -> Dict[str, Any]:
        """
        Ask a question using the QA chain.
        
        Args:
            qa_chain (RetrievalQA): QA chain
            question (str): Question to ask
            
        Returns:
            Dict[str, Any]: Answer and source documents
        """
        try:
            result = qa_chain({"query": question})
            return {
                'success': True,
                'answer': result['result'],
                'source_documents': result.get('source_documents', []),
                'error': None
            }
        except Exception as e:
            logger.error(f"Error asking question: {e}")
            return {
                'success': False,
                'answer': None,
                'source_documents': [],
                'error': f"Error processing question: {str(e)}"
            }
