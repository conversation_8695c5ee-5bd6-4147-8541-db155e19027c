# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Application Configuration
LOG_LEVEL=INFO
CACHE_DIRECTORY=cache
DATABASE_PATH=data/chatbot.db

# UI Configuration
STREAMLIT_THEME=light
MAX_CHAT_HISTORY=50

# Processing Configuration
DEFAULT_CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_TRANSCRIPT_LENGTH=1000000

# Cache Configuration
MAX_CACHE_SIZE_MB=500
ENABLE_VECTORSTORE_CACHE=true

# Security Configuration
MAX_URL_LENGTH=2048
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_MINUTES=60

# Export Configuration
MAX_EXPORT_ENTRIES=1000
PDF_PAGE_SIZE=A4

# Development Configuration (optional)
DEBUG=false
DEVELOPMENT_MODE=false
