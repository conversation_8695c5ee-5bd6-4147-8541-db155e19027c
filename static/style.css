/* YouTube Transcript Chatbot - Custom Styles - Dark Theme */

/* Global dark theme styling */
* {
    box-sizing: border-box;
}

body, html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Aria<PERSON>, sans-serif;
    line-height: 1.6;
    color: #e9ecef !important;
    background-color: #1a1a1a !important;
}

/* Ensure all text elements have proper contrast for dark theme */
p, span, div, label, h1, h2, h3, h4, h5, h6 {
    color: #e9ecef !important;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Main container styling */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #1a1a1a !important;
}

/* Header styling */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.app-header p {
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Card styling - Dark Theme */
.info-card {
    background: #2d3748 !important;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    border-left: 4px solid #667eea;
    color: #e9ecef !important;
}

.success-card {
    background: #1a2e1a !important;
    border-color: #28a745;
    color: #90ee90 !important;
}

.error-card {
    background: #2e1a1a !important;
    border-color: #dc3545;
    color: #ffb3b3 !important;
}

.warning-card {
    background: #2e2a1a !important;
    border-color: #ffc107;
    color: #ffe066 !important;
}

/* Video metadata styling - Dark Theme */
.video-metadata {
    background: #2d3748 !important;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid #4a5568;
}

.video-metadata h4 {
    color: #e9ecef !important;
    margin-bottom: 0.5rem;
}

.metadata-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid #4a5568;
}

.metadata-item:last-child {
    border-bottom: none;
}

.metadata-label {
    font-weight: 600;
    color: #a0aec0 !important;
}

.metadata-value {
    color: #e9ecef !important;
}

/* Chat history styling - Dark Theme */
.chat-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 1rem;
    background: #2d3748 !important;
}

.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 8px;
}

.chat-question {
    background: #1a365d !important;
    border-left: 4px solid #3182ce;
    color: #e9ecef !important;
}

.chat-answer {
    background: #322659 !important;
    border-left: 4px solid #9f7aea;
    margin-left: 1rem;
    color: #e9ecef !important;
}

.chat-timestamp {
    font-size: 0.8rem;
    color: #a0aec0 !important;
    margin-top: 0.5rem;
}

/* Button styling */
.custom-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.custom-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.secondary-button {
    background: #6c757d;
}

.success-button {
    background: #28a745;
}

.danger-button {
    background: #dc3545;
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress bar */
.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Sidebar styling - Dark Theme */
.sidebar-content {
    background: #2d3748 !important;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #4a5568;
}

.sidebar-section {
    margin-bottom: 1.5rem;
}

.sidebar-section h4 {
    color: #e9ecef !important;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

/* Form styling */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
    .app-header h1 {
        font-size: 2rem;
    }
    
    .main-container {
        padding: 10px;
    }
    
    .info-card {
        padding: 1rem;
    }
    
    .chat-answer {
        margin-left: 0.5rem;
    }
}

/* Streamlit specific overrides - Dark Theme */

/* Main app background */
.stApp {
    background-color: #1a1a1a !important;
    color: #e9ecef !important;
}

.stApp > div {
    background-color: #1a1a1a !important;
}

/* Main content area */
.main .block-container {
    background-color: #1a1a1a !important;
    color: #e9ecef !important;
}

/* Sidebar styling */
.css-1d391kg, .css-1lcbmhc, .css-1aumxhk {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
}

/* Button styling */
.stButton > button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.stButton > button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
    color: white !important;
}

/* Input fields */
.stSelectbox > div > div {
    border-radius: 6px !important;
    background-color: #2d3748 !important;
    color: #e9ecef !important;
    border: 1px solid #4a5568 !important;
}

.stSelectbox label {
    color: #e9ecef !important;
}

.stTextInput > div > div > input {
    border-radius: 6px !important;
    background-color: #2d3748 !important;
    color: #e9ecef !important;
    border: 1px solid #4a5568 !important;
}

.stTextInput label {
    color: #e9ecef !important;
}

.stTextArea > div > div > textarea {
    border-radius: 6px !important;
    background-color: #2d3748 !important;
    color: #e9ecef !important;
    border: 1px solid #4a5568 !important;
}

.stTextArea label {
    color: #e9ecef !important;
}

/* Success/Error message styling - Dark Theme */
.stSuccess {
    background: #1a2e1a !important;
    border: 1px solid #28a745 !important;
    border-radius: 6px !important;
    color: #90ee90 !important;
}

.stSuccess p {
    color: #90ee90 !important;
}

.stError {
    background: #2e1a1a !important;
    border: 1px solid #dc3545 !important;
    border-radius: 6px !important;
    color: #ffb3b3 !important;
}

.stError p {
    color: #ffb3b3 !important;
}

.stWarning {
    background: #2e2a1a !important;
    border: 1px solid #ffc107 !important;
    border-radius: 6px !important;
    color: #ffe066 !important;
}

.stWarning p {
    color: #ffe066 !important;
}

.stInfo {
    background: #1a2a2e !important;
    border: 1px solid #17a2b8 !important;
    border-radius: 6px !important;
    color: #66d9ef !important;
}

.stInfo p {
    color: #66d9ef !important;
}

/* Additional dark theme overrides */
.stMarkdown {
    color: #e9ecef !important;
}

.stMarkdown p {
    color: #e9ecef !important;
}

.stMarkdown h1, .stMarkdown h2, .stMarkdown h3, .stMarkdown h4, .stMarkdown h5, .stMarkdown h6 {
    color: #e9ecef !important;
}

/* Expander styling */
.streamlit-expanderHeader {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
    border: 1px solid #4a5568 !important;
}

.streamlit-expanderContent {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
    border: 1px solid #4a5568 !important;
}

/* Metric styling */
.metric-container {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
}

/* Code block styling */
.stCode {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
    border: 1px solid #4a5568 !important;
}

/* DataFrame styling */
.stDataFrame {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
}

/* JSON display styling */
.stJson {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
}

/* Spinner styling */
.stSpinner {
    color: #667eea !important;
}

/* Progress bar styling */
.stProgress .st-bo {
    background-color: #667eea !important;
}

/* Custom classes for dark theme */
.visible-text {
    color: #e9ecef !important;
    background-color: #2d3748 !important;
    padding: 0.5rem !important;
    border-radius: 4px !important;
    border: 1px solid #4a5568 !important;
}

.high-contrast-text {
    color: #ffffff !important;
    background-color: #000000 !important;
    font-weight: 600 !important;
    padding: 0.5rem !important;
    border-radius: 4px !important;
}

/* Override any remaining white backgrounds */
div[data-testid="stSidebar"] {
    background-color: #2d3748 !important;
}

div[data-testid="stSidebar"] > div {
    background-color: #2d3748 !important;
}

.css-1lcbmhc {
    background-color: #2d3748 !important;
}

.css-1d391kg {
    background-color: #1a1a1a !important;
}

/* Force dark theme on all containers */
.element-container {
    background-color: transparent !important;
    color: #e9ecef !important;
}

.stAlert {
    color: #e9ecef !important;
}
