/* YouTube Transcript Chatbot - Custom Styles */

/* Main container styling */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styling */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.app-header p {
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Card styling */
.info-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
}

.success-card {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.error-card {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.warning-card {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

/* Video metadata styling */
.video-metadata {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.video-metadata h4 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.metadata-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid #e9ecef;
}

.metadata-item:last-child {
    border-bottom: none;
}

.metadata-label {
    font-weight: 600;
    color: #6c757d;
}

.metadata-value {
    color: #495057;
}

/* Chat history styling */
.chat-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background: white;
}

.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 8px;
}

.chat-question {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.chat-answer {
    background: #f3e5f5;
    border-left: 4px solid #9c27b0;
    margin-left: 1rem;
}

.chat-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

/* Button styling */
.custom-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.custom-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.secondary-button {
    background: #6c757d;
}

.success-button {
    background: #28a745;
}

.danger-button {
    background: #dc3545;
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress bar */
.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Sidebar styling */
.sidebar-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.sidebar-section {
    margin-bottom: 1.5rem;
}

.sidebar-section h4 {
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

/* Form styling */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
    .app-header h1 {
        font-size: 2rem;
    }
    
    .main-container {
        padding: 10px;
    }
    
    .info-card {
        padding: 1rem;
    }
    
    .chat-answer {
        margin-left: 0.5rem;
    }
}

/* Streamlit specific overrides */
.stButton > button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.stButton > button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.stSelectbox > div > div {
    border-radius: 6px;
}

.stTextInput > div > div > input {
    border-radius: 6px;
}

.stTextArea > div > div > textarea {
    border-radius: 6px;
}

/* Success/Error message styling */
.stSuccess {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
}

.stError {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
}

.stWarning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
}

.stInfo {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 6px;
}
