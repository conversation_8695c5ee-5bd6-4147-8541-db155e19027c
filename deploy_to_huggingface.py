#!/usr/bin/env python3
"""
Deployment helper script for Hugging Face Spaces.
This script helps prepare and validate the deployment package.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_deployment_package():
    """Create a clean deployment package."""
    print("🚀 Creating Hugging Face Spaces deployment package...")
    
    # Create deployment directory
    deploy_dir = Path("huggingface_deployment")
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    deploy_dir.mkdir()
    
    # Files to include in deployment
    files_to_copy = [
        "app.py",
        "README.md",
        "requirements.txt",
        ".streamlit/config.toml",
        "static/style.css",
        "src/",
        "config/",
        "DEPLOYMENT.md",
        "HUGGINGFACE_DEPLOYMENT_CHECKLIST.md"
    ]
    
    # Copy files
    for file_path in files_to_copy:
        src = Path(file_path)
        if src.exists():
            if src.is_dir():
                shutil.copytree(src, deploy_dir / src.name)
                print(f"  ✅ Copied directory: {file_path}")
            else:
                # Create parent directory if needed
                dest = deploy_dir / file_path
                dest.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src, dest)
                print(f"  ✅ Copied file: {file_path}")
        else:
            print(f"  ⚠️  File not found: {file_path}")
    
    print(f"\n✅ Deployment package created in: {deploy_dir}")
    return deploy_dir

def validate_deployment_package(deploy_dir):
    """Validate the deployment package."""
    print("\n🔍 Validating deployment package...")
    
    required_files = [
        "app.py",
        "README.md",
        "requirements.txt",
        ".streamlit/config.toml",
        "static/style.css",
        "src/__init__.py",
        "src/utils/__init__.py",
        "config/__init__.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (deploy_dir / file_path).exists():
            missing_files.append(file_path)
            print(f"  ❌ Missing: {file_path}")
        else:
            print(f"  ✅ Found: {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ All required files present!")
        return True

def check_requirements():
    """Check if requirements.txt is valid."""
    print("\n🔍 Checking requirements.txt...")
    
    try:
        with open("requirements.txt", "r") as f:
            requirements = f.read()
        
        # Check for known problematic combinations
        if "openai==1.0.0" in requirements:
            print("  ❌ Found problematic OpenAI version (1.0.0)")
            return False
        
        if "openai>=1.10.0" in requirements or "openai>=" in requirements:
            print("  ✅ OpenAI version looks good")
        
        print("  ✅ Requirements.txt appears valid")
        return True
        
    except Exception as e:
        print(f"  ❌ Error reading requirements.txt: {e}")
        return False

def generate_deployment_instructions(deploy_dir):
    """Generate deployment instructions."""
    instructions = f"""
# 🚀 Deployment Instructions

Your deployment package is ready in: {deploy_dir}

## Next Steps:

1. **Create Hugging Face Space**:
   - Go to https://huggingface.co/spaces
   - Click "Create new Space"
   - Choose Streamlit SDK

2. **Upload Files**:
   - Upload all files from {deploy_dir} to your Space
   - Maintain the folder structure

3. **Configure Secrets**:
   - In Space settings → Repository secrets
   - Add: OPENAI_API_KEY = your_api_key_here

4. **Wait for Build**:
   - Monitor build logs for any errors
   - Build should complete in 2-5 minutes

## Files Included:
"""
    
    # List all files in deployment package
    for root, dirs, files in os.walk(deploy_dir):
        level = root.replace(str(deploy_dir), '').count(os.sep)
        indent = ' ' * 2 * level
        instructions += f"{indent}- {os.path.basename(root)}/\n"
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            instructions += f"{subindent}- {file}\n"
    
    instructions += """
## Troubleshooting:
- If build fails, check the build logs
- Ensure OPENAI_API_KEY is set correctly
- Verify all files uploaded properly

🎉 Your YouTube Transcript Tutor will be live once deployed!
"""
    
    # Save instructions
    with open(deploy_dir / "DEPLOYMENT_INSTRUCTIONS.md", "w") as f:
        f.write(instructions)
    
    print(instructions)

def main():
    """Main deployment preparation function."""
    print("🎓 YouTube Transcript Tutor - Hugging Face Deployment Helper")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("app.py").exists():
        print("❌ Error: app.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Run validation checks
    checks = [
        ("Requirements", check_requirements),
    ]
    
    for check_name, check_func in checks:
        if not check_func():
            print(f"❌ {check_name} check failed. Please fix the issues before deploying.")
            sys.exit(1)
    
    # Create deployment package
    deploy_dir = create_deployment_package()
    
    # Validate package
    if not validate_deployment_package(deploy_dir):
        print("❌ Deployment package validation failed.")
        sys.exit(1)
    
    # Generate instructions
    generate_deployment_instructions(deploy_dir)
    
    print("\n🎉 Deployment package ready!")
    print(f"📁 Package location: {deploy_dir}")
    print("📖 See DEPLOYMENT_INSTRUCTIONS.md for next steps")

if __name__ == "__main__":
    main()
